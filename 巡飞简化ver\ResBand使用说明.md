# ResBand算法使用说明

## 概述

Resolution Bandit (ResBand) 是一个基于多臂老虎机的自适应分辨率调度算法，用于优化DWA层的控制输入离散化分辨率。该算法能够智能地在探索效率和计算精度之间找到平衡。

## 算法原理

### 核心思想
- 将不同的离散化分辨率配置视为老虎机的"臂"
- 通过UCB策略在线学习选择最优的采样策略
- 实现探索与利用的智能平衡

### 回报函数
```
r_bandit = α × ΔR_m + β × (-ΔL_m_critic) + γ × (-N_m_violation)
```

其中：
- `ΔR_m`: 阶段m与阶段m-1的平均片段奖励之差
- `-ΔL_m_critic`: Critic网络平均损失的下降值
- `-N_m_violation`: 平均约束违反次数的减少
- `α, β, γ`: 权重系数，满足 α + β + γ = 1

## 使用方法

### 1. 基本使用

```bash
# 启用ResBand算法进行训练
python run_staged_training.py --use-resband --start-stage 1 --end-stage 3

# 使用自定义配置
python run_staged_training.py --use-resband --resband-config resband_config_example.json
```

### 2. 对比实验

```bash
# 运行完整的对比实验
python resband_comparison_experiment.py
```

### 3. 编程接口

```python
from resolution_bandit import ResolutionBandit, create_paper_configs

# 创建ResBand算法实例
resband = ResolutionBandit(
    configs=create_paper_configs(),
    exploration_coefficient=2.0,
    stage_length=20,
    reward_weights=(0.7, 0.2, 0.1)
)

# 选择分辨率
resolution = resband.select_resolution(episode)

# 更新性能指标
resband.update_performance(episode, reward, critic_loss, violations)
```

## 配置参数

### 分辨率配置
```python
# 论文中使用的配置
configs = [
    ResolutionConfig("粗分辨率", 3.0, 12.0, 0.3, "计算效率优先"),
    ResolutionConfig("中等分辨率", 1.5, 6.0, 0.15, "论文默认配置"),
    ResolutionConfig("细分辨率", 0.8, 3.0, 0.08, "高精度控制")
]
```

### 算法参数
- `exploration_coefficient`: UCB探索系数，默认2.0
- `stage_length`: 每个阶段的episode数量，默认20
- `reward_weights`: 回报函数权重，默认(0.7, 0.2, 0.1)

## 输出结果

### 1. 算法结果
- `resband_results_*.json`: 详细的算法运行结果
- `resband_analysis_*.png`: 算法性能分析图表

### 2. 对比实验
- `comparison_results.json`: 所有方法的对比结果
- `comparison_plots.png`: 对比图表
- `comparison_report.md`: 详细报告

### 3. 图表说明
- **臂选择历史**: 显示算法在不同episode中选择的分辨率配置
- **回报变化**: 显示每个阶段的回报值和平均回报
- **选择次数分布**: 显示每个分辨率配置被选择的次数
- **最终平均回报**: 显示每个配置的最终性能

## 性能分析

### 优势
1. **自适应优化**: 根据训练进度自动调整分辨率
2. **探索-利用平衡**: UCB策略确保充分探索和有效利用
3. **多维度评估**: 综合考虑奖励、学习稳定性和安全性
4. **计算效率**: 仅在阶段结束时更新，计算开销小

### 适用场景
- 训练初期：倾向于选择粗分辨率以快速探索
- 训练中期：平衡分辨率和精度
- 训练后期：倾向于选择细分辨率以精细优化

## 故障排除

### 常见问题

1. **算法不收敛**
   - 检查回报函数权重设置
   - 调整探索系数
   - 增加阶段长度

2. **性能提升不明显**
   - 检查分辨率配置范围
   - 调整UCB参数
   - 验证性能指标计算

3. **计算开销过大**
   - 减少分辨率配置数量
   - 增加阶段长度
   - 简化回报函数

### 调试技巧

1. **启用详细日志**
   ```python
   # 在训练循环中添加调试信息
   print(f"Episode {episode}: 选择分辨率 {resolution}")
   print(f"性能指标: reward={reward}, loss={critic_loss}, violations={violations}")
   ```

2. **可视化分析**
   ```python
   # 生成分析图表
   resband.plot_results()
   ```

3. **保存中间结果**
   ```python
   # 定期保存算法状态
   resband.save_results(f"checkpoint_{episode}.json")
   ```

## 论文集成

### 在论文中的描述

**3.4 基于元学习的自适应分辨率调度算法**

巡飞弹的大航程与高机动性导致了其控制输入范围大、安全动作集 `U_safe` 规模可变的特点。为确保分层架构的效率，DWA层需对连续控制空间 `u = [a_T, a_N, μ]^T` 进行离散化采样。固定分辨率方案在探索效率与控制精度间难以兼顾：粗分辨率虽计算高效但控制粗糙；细分辨率虽能精细控制但计算成本高昂，且在训练初期易因动作空间过大而降低探索效率。

为解决这一矛盾，我们提出了一种基于多臂老虎机（Multi-Armed Bandit, MAB）的元学习算法——Resolution Bandit (ResBand)。该算法将分辨率配置的选择建模为一个序列决策问题，其核心思想是：**将不同的离散化分辨率配置视为老虎机的"臂"，通过评估每个"臂"（即每种分辨率配置）在阶段性训练中带来的"学习效率回报"，在线学习选择最优的采样策略，从而实现探索与利用的智能平衡。**

### 实验设计

1. **基线方法**
   - Fixed-Coarse: 固定使用粗分辨率
   - Fixed-Medium: 固定使用中等分辨率
   - Fixed-Fine: 固定使用细分辨率
   - Heuristic: 启发式调度（每100个episode切换）

2. **评估指标**
   - 成功率
   - 最终平均奖励
   - 收敛速度
   - 训练时间

3. **结果分析**
   - 性能对比图表
   - 收敛曲线分析
   - 算法选择行为分析

## 扩展功能

### 1. 自定义分辨率配置
```python
# 创建自定义配置
custom_configs = [
    ResolutionConfig("超粗", 5.0, 20.0, 0.8, "最高效率"),
    ResolutionConfig("超细", 0.5, 2.0, 0.05, "最高精度")
]
```

### 2. 动态权重调整
```python
# 根据训练进度调整权重
def adaptive_weights(episode, total_episodes):
    progress = episode / total_episodes
    alpha = 0.7 * (1 - progress) + 0.3 * progress  # 逐渐重视奖励
    beta = 0.2
    gamma = 0.1 * (1 - progress) + 0.3 * progress   # 逐渐重视安全性
    return (alpha, beta, gamma)
```

### 3. 多目标优化
```python
# 扩展回报函数以支持多目标
def multi_objective_reward(reward, loss, violations, energy, smoothness):
    return (α1 * reward + α2 * (-loss) + α3 * (-violations) + 
            α4 * energy + α5 * smoothness)
```

## 总结

ResBand算法为巡飞弹的分层运动规划提供了一个智能的分辨率调度解决方案。通过多臂老虎机框架，算法能够自动适应训练的不同阶段，在计算效率和控制精度之间找到最优平衡。该算法不仅提升了训练效率，还为论文提供了重要的创新点。
