"""
DWA动作分辨率选择实验运行脚本
提供交互式菜单和参数配置选项
"""

import os
import sys
import argparse
from datetime import datetime

def print_experiment_info():
    """打印实验信息"""
    print("🎯 DWA动作分辨率选择仿真实验")
    print("=" * 60)
    print("基于论文4.3节：选取合理离散动作分辨率")
    print()
    print("实验目标：")
    print("1. 验证不同分辨率配置对控制精度的影响")
    print("2. 分析分辨率设置对计算效率的影响") 
    print("3. 确定精度与效率的最优平衡点")
    print("4. 为后续仿真实验提供参数配置依据")
    print()
    print("分辨率配置设计原则：")
    print("- 对轨迹平滑性影响较弱，采用较粗分辨率")
    print("- 与航迹曲率强相关，需中等精度")
    print("- 对三维机动敏感性最高但计算代价大")
    print()

def print_menu():
    """打印菜单选项"""
    print("📋 实验选项：")
    print("1. 运行完整分辨率对比实验")
    print("2. 运行快速验证实验（减少回合数）")
    print("3. 自定义分辨率配置实验")
    print("4. 查看现有实验结果")
    print("5. 生成实验报告")
    print("6. 退出")
    print()

def run_full_experiment():
    """运行完整实验"""
    print("🚀 开始运行完整DWA动作分辨率对比实验...")
    
    try:
        from dwa_action_resolution_experiment import DWAActionResolutionExperiment
        
        # 创建实验实例
        experiment = DWAActionResolutionExperiment()
        
        # 运行实验
        all_results, comparison_results = experiment.run_resolution_comparison_experiment()
        
        print("\n✅ 完整实验完成！")
        print(f"📁 结果保存在: {experiment.output_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 实验运行失败: {e}")
        return False

def run_quick_experiment():
    """运行快速验证实验"""
    print("⚡ 开始运行快速验证实验...")
    
    try:
        from dwa_action_resolution_experiment import DWAActionResolutionExperiment
        
        # 创建实验实例并修改参数
        experiment = DWAActionResolutionExperiment("quick_dwa_resolution_experiment_results")
        experiment.episodes_per_config = 5  # 减少回合数
        
        # 只测试部分配置
        experiment.resolution_configs = experiment.resolution_configs[:3]  # 只测试前3个配置
        
        # 运行实验
        all_results, comparison_results = experiment.run_resolution_comparison_experiment()
        
        print("\n✅ 快速验证实验完成！")
        print(f"📁 结果保存在: {experiment.output_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 快速实验运行失败: {e}")
        return False

def run_custom_experiment():
    """运行自定义配置实验"""
    print("🔧 自定义分辨率配置实验")
    print("=" * 40)
    
    try:
        from dwa_action_resolution_experiment import DWAResolutionConfig, DWAActionResolutionExperiment
        
        # 获取用户输入
        print("请输入自定义分辨率配置：")
        
        configs = []
        while True:
            print(f"\n配置 {len(configs) + 1}:")
            name = input("配置名称 (或输入'done'结束): ").strip()
            if name.lower() == 'done':
                break
                
            try:
                a_T = float(input("切向加速度分辨率: "))
                a_N = float(input("法向加速度分辨率: "))
                mu = float(input("倾斜角分辨率: "))
                desc = input("配置描述: ").strip()
                
                config = DWAResolutionConfig(name, a_T, a_N, mu, desc)
                configs.append(config)
                
            except ValueError:
                print("❌ 输入格式错误，请重新输入")
                continue
        
        if not configs:
            print("❌ 未输入任何配置，退出")
            return False
        
        # 创建实验实例
        experiment = DWAActionResolutionExperiment("custom_dwa_resolution_experiment_results")
        experiment.resolution_configs = configs
        
        # 运行实验
        all_results, comparison_results = experiment.run_resolution_comparison_experiment()
        
        print("\n✅ 自定义实验完成！")
        print(f"📁 结果保存在: {experiment.output_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 自定义实验运行失败: {e}")
        return False

def view_existing_results():
    """查看现有实验结果"""
    print("📊 查看现有实验结果")
    print("=" * 40)
    
    # 查找可能的结果目录
    possible_dirs = [
        "dwa_resolution_experiment_results",
        "quick_dwa_resolution_experiment_results", 
        "custom_dwa_resolution_experiment_results"
    ]
    
    found_results = []
    for dir_name in possible_dirs:
        if os.path.exists(dir_name):
            found_results.append(dir_name)
    
    if not found_results:
        print("❌ 未找到现有实验结果")
        return
    
    print("找到以下结果目录：")
    for i, dir_name in enumerate(found_results, 1):
        print(f"{i}. {dir_name}")
    
    try:
        choice = int(input("\n请选择要查看的结果目录 (输入序号): ")) - 1
        if 0 <= choice < len(found_results):
            selected_dir = found_results[choice]
            show_result_summary(selected_dir)
        else:
            print("❌ 无效选择")
    except ValueError:
        print("❌ 输入格式错误")

def show_result_summary(result_dir):
    """显示结果摘要"""
    print(f"\n📋 结果摘要: {result_dir}")
    print("=" * 50)
    
    # 检查报告文件
    report_path = os.path.join(result_dir, "reports", "dwa_resolution_experiment_report.md")
    if os.path.exists(report_path):
        print("📝 实验报告: 已生成")
        
        # 读取报告的前几行
        try:
            with open(report_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()[:20]  # 读取前20行
                print("\n报告摘要:")
                for line in lines:
                    if line.strip() and not line.startswith('#'):
                        print(line.strip())
        except Exception as e:
            print(f"读取报告失败: {e}")
    else:
        print("📝 实验报告: 未找到")
    
    # 检查图表文件
    plots_dir = os.path.join(result_dir, "plots")
    if os.path.exists(plots_dir):
        plot_files = [f for f in os.listdir(plots_dir) if f.endswith('.png')]
        print(f"\n📊 生成的图表: {len(plot_files)} 个")
        for plot_file in plot_files:
            print(f"  - {plot_file}")
    else:
        print("\n📊 生成的图表: 未找到")
    
    # 检查结果文件
    results_dir = os.path.join(result_dir, "results")
    if os.path.exists(results_dir):
        result_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
        print(f"\n📈 结果文件: {len(result_files)} 个")
        for result_file in result_files:
            print(f"  - {result_file}")

def generate_report():
    """生成实验报告"""
    print("📝 生成实验报告")
    print("=" * 30)
    
    # 查找可能的结果目录
    possible_dirs = [
        "dwa_resolution_experiment_results",
        "quick_dwa_resolution_experiment_results", 
        "custom_dwa_resolution_experiment_results"
    ]
    
    found_results = []
    for dir_name in possible_dirs:
        if os.path.exists(dir_name):
            found_results.append(dir_name)
    
    if not found_results:
        print("❌ 未找到现有实验结果，无法生成报告")
        return
    
    print("找到以下结果目录：")
    for i, dir_name in enumerate(found_results, 1):
        print(f"{i}. {dir_name}")
    
    try:
        choice = int(input("\n请选择要生成报告的结果目录 (输入序号): ")) - 1
        if 0 <= choice < len(found_results):
            selected_dir = found_results[choice]
            generate_summary_report(selected_dir)
        else:
            print("❌ 无效选择")
    except ValueError:
        print("❌ 输入格式错误")

def generate_summary_report(result_dir):
    """生成摘要报告"""
    print(f"\n📝 为 {result_dir} 生成摘要报告...")
    
    try:
        import json
        from datetime import datetime
        
        # 读取结果文件
        results_dir = os.path.join(result_dir, "results")
        if not os.path.exists(results_dir):
            print("❌ 结果目录不存在")
            return
        
        result_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
        if not result_files:
            print("❌ 未找到结果文件")
            return
        
        # 生成摘要报告
        summary_path = os.path.join(result_dir, f"summary_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("# DWA动作分辨率实验摘要报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**结果目录**: {result_dir}\n\n")
            
            f.write("## 实验结果概览\n\n")
            
            # 读取并分析每个结果文件
            for result_file in result_files:
                config_name = result_file.replace('_results.json', '')
                filepath = os.path.join(results_dir, result_file)
                
                try:
                    with open(filepath, 'r', encoding='utf-8') as rf:
                        data = json.load(rf)
                    
                    perf_metrics = data.get("performance_metrics", {})
                    comp_metrics = data.get("computational_metrics", {})
                    
                    f.write(f"### {config_name} 配置\n\n")
                    f.write(f"- **成功率**: {perf_metrics.get('overall_success_rate', 0):.3f}\n")
                    f.write(f"- **控制精度**: {perf_metrics.get('average_control_accuracy', 0):.3f}\n")
                    f.write(f"- **轨迹平滑度**: {perf_metrics.get('average_trajectory_smoothness', 0):.3f}\n")
                    f.write(f"- **计算时间**: {comp_metrics.get('average_computation_time', 0):.3f}s\n")
                    f.write(f"- **计算效率**: {comp_metrics.get('computational_efficiency', 0):.3f}\n\n")
                    
                except Exception as e:
                    f.write(f"### {config_name} 配置\n\n")
                    f.write(f"读取失败: {e}\n\n")
        
        print(f"✅ 摘要报告已生成: {summary_path}")
        
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")

def interactive_mode():
    """交互式模式"""
    while True:
        print_experiment_info()
        print_menu()
        
        try:
            choice = input("请选择操作 (1-6): ").strip()
            
            if choice == '1':
                run_full_experiment()
            elif choice == '2':
                run_quick_experiment()
            elif choice == '3':
                run_custom_experiment()
            elif choice == '4':
                view_existing_results()
            elif choice == '5':
                generate_report()
            elif choice == '6':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        input("\n按回车键继续...")
        os.system('cls' if os.name == 'nt' else 'clear')

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DWA动作分辨率选择仿真实验')
    parser.add_argument('--mode', choices=['interactive', 'full', 'quick', 'custom'], 
                       default='interactive', help='运行模式')
    parser.add_argument('--output-dir', default='dwa_resolution_experiment_results',
                       help='输出目录')
    
    args = parser.parse_args()
    
    if args.mode == 'interactive':
        interactive_mode()
    elif args.mode == 'full':
        run_full_experiment()
    elif args.mode == 'quick':
        run_quick_experiment()
    elif args.mode == 'custom':
        run_custom_experiment()

if __name__ == "__main__":
    main()


