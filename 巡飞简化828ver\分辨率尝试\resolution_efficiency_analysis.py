"""
DWA分辨率与探索效率和训练效率关系分析
分析分辨率设置对探索效率和训练效率的综合影响
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd

class ResolutionEfficiencyAnalyzer:
    """分辨率效率分析器"""
    
    def __init__(self):
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 分辨率配置
        self.resolutions = {
            "ultra_coarse": {"a_T": 8.0, "a_N": 20.0, "mu": 1.0},
            "coarse": {"a_T": 4.0, "a_N": 15.0, "mu": 0.5},
            "medium": {"a_T": 2.0, "a_N": 8.0, "mu": 0.25},
            "fine": {"a_T": 1.0, "a_N": 4.0, "mu": 0.1},
            "ultra_fine": {"a_T": 0.5, "a_N": 2.0, "mu": 0.05}
        }
        
    def analyze_exploration_efficiency(self):
        """分析探索效率"""
        print("🔍 分析分辨率对探索效率的影响")
        print("=" * 50)
        
        exploration_metrics = {}
        
        for name, config in self.resolutions.items():
            # 计算动作集大小
            a_T_actions = int(16 / config["a_T"])  # 假设a_T范围是0-8
            a_N_actions = int(40 / config["a_N"])  # 假设a_N范围是0-40
            mu_actions = int(2 / config["mu"])     # 假设mu范围是-1到1
            
            total_actions = a_T_actions * a_N_actions * mu_actions
            
            # 探索效率指标
            exploration_coverage = min(1.0, total_actions / 1000)  # 动作空间覆盖度
            exploration_speed = 1000 / total_actions  # 探索速度（动作/秒）
            exploration_precision = 1.0 / (config["a_T"] + config["a_N"] + config["mu"])  # 探索精度
            
            exploration_metrics[name] = {
                "total_actions": total_actions,
                "coverage": exploration_coverage,
                "speed": exploration_speed,
                "precision": exploration_precision,
                "efficiency_score": exploration_coverage * exploration_speed * exploration_precision
            }
            
            print(f"{name}:")
            print(f"  动作集大小: {total_actions}")
            print(f"  覆盖度: {exploration_coverage:.3f}")
            print(f"  探索速度: {exploration_speed:.1f}")
            print(f"  探索精度: {exploration_precision:.3f}")
            print(f"  综合效率: {exploration_metrics[name]['efficiency_score']:.3f}")
            print()
        
        return exploration_metrics
    
    def analyze_training_efficiency(self):
        """分析训练效率"""
        print("🏋️ 分析分辨率对训练效率的影响")
        print("=" * 50)
        
        training_metrics = {}
        
        for name, config in self.resolutions.items():
            # 计算计算复杂度
            a_T_actions = int(16 / config["a_T"])
            a_N_actions = int(40 / config["a_N"])
            mu_actions = int(2 / config["mu"])
            total_actions = a_T_actions * a_N_actions * mu_actions
            
            # 训练效率指标
            computation_time = total_actions * 0.001  # 每个动作1ms
            memory_usage = total_actions * 0.1  # 每个动作0.1KB
            convergence_speed = 1.0 / (1.0 + computation_time)  # 收敛速度
            training_stability = 1.0 / (1.0 + config["a_T"] + config["a_N"] + config["mu"])  # 训练稳定性
            
            training_metrics[name] = {
                "computation_time": computation_time,
                "memory_usage": memory_usage,
                "convergence_speed": convergence_speed,
                "stability": training_stability,
                "efficiency_score": convergence_speed * training_stability / (1.0 + computation_time)
            }
            
            print(f"{name}:")
            print(f"  计算时间: {computation_time:.3f}s")
            print(f"  内存使用: {memory_usage:.1f}KB")
            print(f"  收敛速度: {convergence_speed:.3f}")
            print(f"  训练稳定性: {training_stability:.3f}")
            print(f"  综合效率: {training_metrics[name]['efficiency_score']:.3f}")
            print()
        
        return training_metrics
    
    def analyze_combined_efficiency(self, exploration_metrics, training_metrics):
        """分析综合效率"""
        print("⚖️ 分析综合效率（探索+训练）")
        print("=" * 50)
        
        combined_metrics = {}
        
        for name in self.resolutions.keys():
            exp_eff = exploration_metrics[name]["efficiency_score"]
            train_eff = training_metrics[name]["efficiency_score"]
            
            # 不同权重组合
            exploration_weighted = 0.6 * exp_eff + 0.4 * train_eff  # 偏向探索
            training_weighted = 0.4 * exp_eff + 0.6 * train_eff     # 偏向训练
            balanced = 0.5 * exp_eff + 0.5 * train_eff              # 平衡
            
            combined_metrics[name] = {
                "exploration_weighted": exploration_weighted,
                "training_weighted": training_weighted,
                "balanced": balanced,
                "exploration_efficiency": exp_eff,
                "training_efficiency": train_eff
            }
            
            print(f"{name}:")
            print(f"  探索加权: {exploration_weighted:.3f}")
            print(f"  训练加权: {training_weighted:.3f}")
            print(f"  平衡加权: {balanced:.3f}")
            print()
        
        return combined_metrics
    
    def plot_efficiency_analysis(self, exploration_metrics, training_metrics, combined_metrics):
        """绘制效率分析图表"""
        print("📊 生成效率分析图表...")
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('DWA分辨率与探索效率和训练效率关系分析', fontsize=16, fontweight='bold')
        
        config_names = list(self.resolutions.keys())
        
        # 1. 探索效率对比
        exploration_scores = [exploration_metrics[name]["efficiency_score"] for name in config_names]
        axes[0, 0].bar(config_names, exploration_scores, color='skyblue', alpha=0.7)
        axes[0, 0].set_title('探索效率对比')
        axes[0, 0].set_ylabel('探索效率得分')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 训练效率对比
        training_scores = [training_metrics[name]["efficiency_score"] for name in config_names]
        axes[0, 1].bar(config_names, training_scores, color='lightgreen', alpha=0.7)
        axes[0, 1].set_title('训练效率对比')
        axes[0, 1].set_ylabel('训练效率得分')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 探索vs训练效率散点图
        axes[1, 0].scatter(exploration_scores, training_scores, s=200, alpha=0.7, c=range(len(config_names)), cmap='viridis')
        for i, name in enumerate(config_names):
            axes[1, 0].annotate(name, (exploration_scores[i], training_scores[i]), 
                               xytext=(5, 5), textcoords='offset points', fontsize=10)
        axes[1, 0].set_xlabel('探索效率')
        axes[1, 0].set_ylabel('训练效率')
        axes[1, 0].set_title('探索效率 vs 训练效率')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 综合效率对比
        balanced_scores = [combined_metrics[name]["balanced"] for name in config_names]
        axes[1, 1].bar(config_names, balanced_scores, color='orange', alpha=0.7)
        axes[1, 1].set_title('综合效率对比（平衡权重）')
        axes[1, 1].set_ylabel('综合效率得分')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('resolution_efficiency_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 效率分析图表已生成: resolution_efficiency_analysis.png")
    
    def generate_recommendations(self, combined_metrics):
        """生成分辨率选择建议"""
        print("💡 分辨率选择建议")
        print("=" * 50)
        
        # 找出最佳配置
        best_exploration = max(combined_metrics.items(), key=lambda x: x[1]["exploration_efficiency"])
        best_training = max(combined_metrics.items(), key=lambda x: x[1]["training_efficiency"])
        best_balanced = max(combined_metrics.items(), key=lambda x: x[1]["balanced"])
        
        print("🎯 最佳配置推荐:")
        print(f"  最佳探索效率: {best_exploration[0]} (得分: {best_exploration[1]['exploration_efficiency']:.3f})")
        print(f"  最佳训练效率: {best_training[0]} (得分: {best_training[1]['training_efficiency']:.3f})")
        print(f"  最佳综合效率: {best_balanced[0]} (得分: {best_balanced[1]['balanced']:.3f})")
        print()
        
        print("📋 选择建议:")
        print("1. 早期训练阶段（探索为主）: 选择粗分辨率，快速探索动作空间")
        print("2. 中期训练阶段（平衡）: 选择中等分辨率，平衡探索和利用")
        print("3. 后期训练阶段（精细优化）: 选择细分辨率，精细优化策略")
        print("4. 自适应策略: 根据训练进度动态调整分辨率")
        print()
        
        print("⚖️ 权衡考虑:")
        print("- 粗分辨率: 探索效率高，训练效率低")
        print("- 细分辨率: 探索效率低，训练效率高")
        print("- 中等分辨率: 在两者间取得平衡")
        print("- 自适应分辨率: 根据学习阶段动态调整")

def main():
    """主函数"""
    print("🎯 DWA分辨率与探索效率和训练效率关系分析")
    print("=" * 60)
    
    analyzer = ResolutionEfficiencyAnalyzer()
    
    # 分析探索效率
    exploration_metrics = analyzer.analyze_exploration_efficiency()
    
    # 分析训练效率
    training_metrics = analyzer.analyze_training_efficiency()
    
    # 分析综合效率
    combined_metrics = analyzer.analyze_combined_efficiency(exploration_metrics, training_metrics)
    
    # 生成图表
    analyzer.plot_efficiency_analysis(exploration_metrics, training_metrics, combined_metrics)
    
    # 生成建议
    analyzer.generate_recommendations(combined_metrics)
    
    print("\n🎉 分析完成！")

if __name__ == "__main__":
    main()


