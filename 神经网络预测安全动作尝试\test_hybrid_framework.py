"""
测试混合安全约束预测框架
验证硬约束保证 vs 神经网络预测的区别
"""

import sys
import os
import numpy as np
import torch
import time

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hybrid_safe_constraint_predictor import HybridSafeConstraintPredictor
from neural_enhanced_rl_framework import NeuralEnhancedSafeRL
from test_neural_enhanced_framework import MockEnvironment

def test_safety_guarantee():
    """测试安全性保证"""
    print("🔒 测试安全性保证...")
    
    # 创建混合预测器
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    hybrid_predictor = HybridSafeConstraintPredictor(device=device)
    
    # 创建测试环境
    env = MockEnvironment()
    state = env.reset()
    
    print(f"初始状态: {state}")
    print(f"目标位置: {env.goal}")
    print(f"环境边界: {hybrid_predictor.bounds}")
    print(f"障碍物数量: {len(env.obstacles)}")
    
    # 测试多次动作生成
    violation_count = 0
    total_tests = 50
    
    for test_i in range(total_tests):
        # 生成安全动作集
        safe_actions = hybrid_predictor.generate_safe_action_set(
            state, env.obstacles, env.goal, num_candidates=100
        )
        
        if not safe_actions:
            print(f"  测试 {test_i}: 无安全动作")
            continue
        
        # 验证每个"安全"动作是否真的安全
        for action_info in safe_actions[:5]:  # 检查前5个动作
            action = action_info['action']
            
            # 手动验证安全性
            trajectory = hybrid_predictor._predict_trajectory(state, action)
            is_actually_safe = hybrid_predictor._verify_trajectory_safety(
                trajectory, env.obstacles, state
            )
            
            if not is_actually_safe:
                violation_count += 1
                print(f"  ❌ 测试 {test_i}: 发现不安全动作! {action}")
                break
    
    safety_rate = (total_tests * 5 - violation_count) / (total_tests * 5) * 100
    print(f"\n📊 安全性测试结果:")
    print(f"  总测试: {total_tests * 5} 个动作")
    print(f"  违反约束: {violation_count} 次")
    print(f"  安全率: {safety_rate:.1f}%")
    
    return safety_rate

def test_boundary_constraints():
    """专门测试边界约束"""
    print("\n🚧 测试边界约束...")
    
    device = 'cpu'  # 使用CPU避免导入问题
    hybrid_predictor = HybridSafeConstraintPredictor(device=device)
    
    # 创建接近边界的状态
    boundary_states = [
        np.array([50.0, 50.0, 50.0, 0.0, 0.0, 0.0]),      # 接近原点
        np.array([950.0, 950.0, 50.0, 0.0, 0.0, 0.0]),    # 接近边界
        np.array([500.0, 950.0, 90.0, 0.0, 0.0, 0.0]),    # 接近上边界
    ]
    
    goal = np.array([500.0, 500.0, 50.0])
    obstacles = []  # 无障碍物，专注测试边界
    
    for i, state in enumerate(boundary_states):
        print(f"\n  测试边界状态 {i+1}: {state[:3]}")
        
        safe_actions = hybrid_predictor.generate_safe_action_set(
            state, obstacles, goal, num_candidates=50
        )
        
        print(f"    生成安全动作数: {len(safe_actions)}")
        
        # 验证所有动作都不会越界
        boundary_violations = 0
        for action_info in safe_actions:
            trajectory = action_info['trajectory']
            
            # 检查轨迹是否越界
            if (trajectory < hybrid_predictor.bounds_min).any() or \
               (trajectory > hybrid_predictor.bounds).any():
                boundary_violations += 1
        
        print(f"    边界违反: {boundary_violations} / {len(safe_actions)}")

def compare_with_traditional_dwa():
    """与传统DWA对比"""
    print("\n⚖️ 与传统DWA对比...")
    
    # 导入传统DWA（如果可用）
    try:
        sys.path.append('简化ver')
        from td3_dwa_rl_architecture import SafeActionSetGenerator
        dwa_available = True
    except:
        print("  传统DWA不可用，跳过对比")
        return
    
    device = 'cpu'
    hybrid_predictor = HybridSafeConstraintPredictor(device=device)
    
    # 传统DWA配置
    dwa_config = {
        'dt': 0.1,
        'predict_time': 2.0,
        'max_velocity': [30, 30, 30],
        'max_acceleration': [15, 15, 15],
        'velocity_resolution': 0.5,
        'min_safe_distance': 5.0
    }
    traditional_dwa = SafeActionSetGenerator(dwa_config)
    
    # 测试环境
    env = MockEnvironment()
    state = env.reset()
    
    # 性能对比
    num_tests = 10
    
    # 测试混合方法
    hybrid_times = []
    hybrid_action_counts = []
    
    for _ in range(num_tests):
        start_time = time.time()
        safe_actions = hybrid_predictor.generate_safe_action_set(
            state, env.obstacles, env.goal, num_candidates=100
        )
        hybrid_times.append(time.time() - start_time)
        hybrid_action_counts.append(len(safe_actions))
    
    # 测试传统DWA
    dwa_times = []
    dwa_action_counts = []
    
    for _ in range(num_tests):
        start_time = time.time()
        safe_actions = traditional_dwa.generate_safe_action_set(
            state, env.goal, env.obstacles, target_actions=20
        )
        dwa_times.append(time.time() - start_time)
        dwa_action_counts.append(len(safe_actions))
    
    print(f"\n📊 性能对比结果:")
    print(f"  混合方法:")
    print(f"    平均时间: {np.mean(hybrid_times):.4f}s")
    print(f"    平均动作数: {np.mean(hybrid_action_counts):.1f}")
    print(f"  传统DWA:")
    print(f"    平均时间: {np.mean(dwa_times):.4f}s")
    print(f"    平均动作数: {np.mean(dwa_action_counts):.1f}")
    
    speedup = np.mean(dwa_times) / np.mean(hybrid_times)
    print(f"  速度提升: {speedup:.1f}x")

def test_complete_episode():
    """测试完整episode"""
    print("\n🎮 测试完整episode...")
    
    device = 'cpu'
    rl_framework = NeuralEnhancedSafeRL(state_dim=6, action_dim=3, device=device)
    env = MockEnvironment()
    
    state = env.reset()
    total_reward = 0
    steps = 0
    max_steps = 100
    
    trajectory_points = []
    safety_violations = 0
    
    print(f"开始episode测试...")
    
    for step in range(max_steps):
        # 记录轨迹
        trajectory_points.append(state[:3].copy())
        
        # 选择动作
        action, info = rl_framework.get_action(state, env.obstacles, env.goal, training=False)
        
        # 执行动作
        next_state, reward, done, env_info = env.step(action)
        
        # 检查是否违反约束
        if env_info.get('collision', False) or env_info.get('out_of_bounds', False):
            safety_violations += 1
            print(f"  ❌ 步骤 {step}: 安全违反! {'碰撞' if env_info.get('collision') else '越界'}")
        
        total_reward += reward
        steps += 1
        state = next_state
        
        # 每20步打印一次
        if step % 20 == 0:
            goal_dist = np.linalg.norm(state[:3] - env.goal)
            print(f"  步骤 {step}: 距离 {goal_dist:.1f}m, 奖励 {reward:.2f}, 方法 {info['selection_method']}")
        
        if done:
            if env_info.get('success', False):
                print(f"  ✅ 成功到达目标! 步骤: {steps}")
            break
    
    final_dist = np.linalg.norm(state[:3] - env.goal)
    
    print(f"\n📈 Episode结果:")
    print(f"  总步骤: {steps}")
    print(f"  总奖励: {total_reward:.1f}")
    print(f"  最终距离: {final_dist:.1f}m")
    print(f"  安全违反: {safety_violations} 次")
    print(f"  安全率: {(1 - safety_violations/steps)*100:.1f}%")
    
    return safety_violations == 0

def main():
    """主测试函数"""
    print("🚀 混合安全约束预测框架测试")
    print("=" * 60)
    
    try:
        # 1. 测试安全性保证
        safety_rate = test_safety_guarantee()
        
        # 2. 测试边界约束
        test_boundary_constraints()
        
        # 3. 与传统DWA对比
        compare_with_traditional_dwa()
        
        # 4. 测试完整episode
        episode_safe = test_complete_episode()
        
        print("\n" + "=" * 60)
        print("📊 测试总结:")
        print(f"  安全性保证: {safety_rate:.1f}%")
        print(f"  Episode安全: {'✅' if episode_safe else '❌'}")
        
        if safety_rate >= 99.0 and episode_safe:
            print("✅ 混合框架测试通过! 安全性得到保证")
        else:
            print("⚠️ 混合框架需要进一步优化")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
