# DWA动作分辨率选择仿真实验说明

## 实验概述

本实验基于论文4.3节"选取合理离散动作分辨率"的要求，专门设计用于探究改进DWA窗口法在筛选安全动作集时设置不同分辨率对控制精度和训练效率的影响。

### 实验背景

根据巡飞弹运动特性，本文设计了控制量选取准则：
- **对轨迹平滑性影响较弱**：采用较粗分辨率
- **与航迹曲率强相关**：需中等精度
- **对三维机动敏感性最高但计算代价大**：需要在精度与效率间权衡

### 实验目标

1. **验证不同分辨率配置对控制精度的影响**
2. **分析分辨率设置对计算效率的影响**
3. **确定精度与效率的最优平衡点**
4. **为后续仿真实验提供参数配置依据**

## 分辨率配置设计

### 配置参数说明

本实验设计了5种不同的分辨率配置：

| 配置名称 | 切向加速度分辨率 | 法向加速度分辨率 | 倾斜角分辨率 | 设计理念 |
|---------|----------------|----------------|-------------|----------|
| coarse | 4.0 m/s² | 15.0 m/s² | 0.5 rad | 粗分辨率：计算效率优先，适合简单环境 |
| medium | 2.0 m/s² | 8.0 m/s² | 0.25 rad | 中等分辨率：精度与效率平衡，适合一般环境 |
| fine | 1.0 m/s² | 4.0 m/s² | 0.1 rad | 细分辨率：高精度优先，适合复杂环境 |
| ultra_fine | 0.5 m/s² | 2.0 m/s² | 0.05 rad | 超细分辨率：最高精度，计算代价最大 |
| adaptive | 1.5 m/s² | 6.0 m/s² | 0.15 rad | 自适应分辨率：根据环境动态调整，推荐配置 |

### 参数选择依据

1. **切向加速度分辨率 (a_T_resolution)**
   - 影响轨迹平滑性
   - 较粗分辨率对轨迹平滑性影响较弱
   - 范围：0.5 - 4.0 m/s²

2. **法向加速度分辨率 (a_N_resolution)**
   - 与航迹曲率强相关
   - 需要中等精度以保证控制效果
   - 范围：2.0 - 15.0 m/s²

3. **倾斜角分辨率 (mu_resolution)**
   - 对三维机动敏感性最高
   - 计算代价大，需要权衡
   - 范围：0.05 - 0.5 rad

## 实验环境配置

### 测试环境

实验在三种不同复杂度的环境中进行测试：

1. **简单环境 (simple)**
   - 静态障碍物：3-5个
   - 环境复杂度：低
   - 适合粗分辨率配置

2. **复杂静态环境 (complex_static)**
   - 静态障碍物：15-20个
   - 环境复杂度：中等
   - 适合中等分辨率配置

3. **复杂动态环境 (complex_dynamic)**
   - 静态障碍物：15-20个
   - 动态障碍物：2-4个
   - 环境复杂度：高
   - 适合细分辨率配置

### 实验参数

- **每个配置测试回合数**：20回合
- **快速验证回合数**：5回合
- **随机种子**：确保结果可重现

## 性能评估指标

### 控制性能指标

1. **成功率 (Success Rate)**
   - 定义：成功到达目标的回合数 / 总回合数
   - 权重：40%

2. **控制精度 (Control Accuracy)**
   - 定义：实际轨迹与期望轨迹的偏差
   - 权重：30%

3. **轨迹平滑度 (Trajectory Smoothness)**
   - 定义：轨迹的连续性和平滑性
   - 权重：30%

### 计算效率指标

1. **计算时间 (Computation Time)**
   - 定义：每次动作选择所需的计算时间
   - 单位：秒

2. **动作集大小 (Action Set Size)**
   - 定义：DWA生成的安全动作集大小
   - 反映计算复杂度

3. **计算效率 (Computational Efficiency)**
   - 定义：控制精度 / 计算时间
   - 综合评估指标

### 约束满足指标

1. **约束违反次数 (Constraint Violations)**
   - 速度约束违反
   - 加速度约束违反
   - 安全距离约束违反

## 实验运行方式

### 1. 交互式运行

```bash
python run_dwa_resolution_experiment.py
```

提供以下选项：
- 运行完整分辨率对比实验
- 运行快速验证实验（减少回合数）
- 自定义分辨率配置实验
- 查看现有实验结果
- 生成实验报告

### 2. 命令行运行

```bash
# 运行完整实验
python run_dwa_resolution_experiment.py --mode full

# 运行快速验证
python run_dwa_resolution_experiment.py --mode quick

# 自定义配置实验
python run_dwa_resolution_experiment.py --mode custom
```

### 3. 直接运行实验模块

```bash
python dwa_action_resolution_experiment.py
```

## 输出结果说明

### 目录结构

```
dwa_resolution_experiment_results/
├── results/                    # 详细结果数据
│   ├── coarse_results.json
│   ├── medium_results.json
│   ├── fine_results.json
│   ├── ultra_fine_results.json
│   └── adaptive_results.json
├── plots/                      # 可视化图表
│   ├── overall_performance_comparison.png
│   ├── computational_efficiency_comparison.png
│   ├── resolution_impact_analysis.png
│   ├── environment_sensitivity_analysis.png
│   └── performance_efficiency_tradeoff.png
└── reports/                    # 实验报告
    └── dwa_resolution_experiment_report.md
```

### 结果文件格式

#### JSON结果文件结构

```json
{
  "config": {
    "a_T_resolution": 1.5,
    "a_N_resolution": 6.0,
    "mu_resolution": 0.15,
    "description": "自适应分辨率配置"
  },
  "environments": {
    "simple": {
      "success_rate": 0.95,
      "average_path_length": 120.5,
      "average_completion_time": 45.2,
      "average_control_accuracy": 0.88,
      "constraint_violations": 2,
      "average_computation_time": 0.15,
      "action_set_sizes": [25, 28, 22, ...],
      "trajectory_smoothness": 0.82,
      "detailed_episodes": [...]
    }
  },
  "performance_metrics": {
    "overall_success_rate": 0.92,
    "average_path_length": 135.8,
    "average_completion_time": 52.1,
    "average_control_accuracy": 0.85,
    "total_constraint_violations": 8,
    "average_trajectory_smoothness": 0.79
  },
  "computational_metrics": {
    "average_computation_time": 0.18,
    "average_action_set_size": 26.5,
    "computational_efficiency": 4.72
  }
}
```

### 可视化图表说明

1. **综合性能对比图**
   - 成功率、控制精度、轨迹平滑度、约束违反次数对比

2. **计算效率对比图**
   - 计算时间、动作集大小、计算效率、综合评分对比

3. **分辨率影响分析图**
   - 各分辨率参数对性能和效率的影响散点图

4. **环境敏感性分析图**
   - 不同环境下各配置的性能变化趋势

5. **性能-效率权衡图**
   - 帕累托前沿分析，找出最优配置

## 实验报告内容

### 报告结构

1. **实验概述**
   - 实验目标和背景
   - 分辨率配置设计

2. **实验结果分析**
   - 最佳性能配置
   - 最佳效率配置
   - 推荐配置

3. **详细性能对比**
   - 综合性能指标对比表
   - 各环境下的表现分析

4. **结论与建议**
   - 分辨率选择原则
   - 实际应用建议

## 技术实现细节

### 核心算法

1. **DWA动作生成**
   ```python
   def generate_safe_control_set(self, current_state, obstacles, goal, max_actions=20):
       # 根据分辨率配置生成离散动作集
       # 预测轨迹并评估安全性
       # 返回安全动作集
   ```

2. **性能评估**
   ```python
   def _simulate_single_episode(self, config, env_name, episode_id):
       # 模拟单个测试回合
       # 计算各种性能指标
       # 返回详细结果
   ```

3. **对比分析**
   ```python
   def _generate_comparison_analysis(self, all_results):
       # 找出最佳配置
       # 分析分辨率影响
       # 生成推荐结果
   ```

### 依赖库

- **numpy**: 数值计算
- **matplotlib**: 图表生成
- **seaborn**: 统计可视化
- **pandas**: 数据处理
- **json**: 结果保存

## 使用建议

### 首次使用

1. **运行快速验证**：先运行快速验证实验，确认系统正常工作
2. **查看结果**：检查生成的图表和报告
3. **运行完整实验**：进行完整的对比分析

### 自定义配置

1. **根据实际需求**：根据具体的巡飞弹参数调整分辨率范围
2. **环境适配**：根据实际环境复杂度调整配置
3. **性能优化**：根据计算资源限制选择合适的配置

### 结果分析

1. **关注帕累托前沿**：找出性能-效率的最优平衡点
2. **环境敏感性**：分析不同环境下的表现差异
3. **实际应用**：根据具体应用场景选择合适配置

## 注意事项

1. **计算资源**：超细分辨率配置计算量大，需要足够的计算资源
2. **随机性**：实验包含随机性，建议多次运行取平均值
3. **环境一致性**：确保不同配置在相同环境下测试
4. **参数范围**：分辨率参数应在合理范围内，避免过小或过大

## 扩展功能

### 可能的改进

1. **自适应分辨率**：根据环境复杂度动态调整分辨率
2. **多目标优化**：使用多目标优化算法寻找最优配置
3. **实时调整**：在运行过程中根据性能动态调整分辨率
4. **机器学习优化**：使用机器学习方法自动优化分辨率参数

### 集成到主系统

本实验模块可以集成到主仿真系统中：

1. **作为预实验**：为后续仿真提供参数配置依据
2. **动态配置**：根据环境自动选择合适的分辨率配置
3. **性能监控**：实时监控分辨率配置对性能的影响

## 联系与支持

如有问题或建议，请参考：
- 实验代码：`dwa_action_resolution_experiment.py`
- 运行脚本：`run_dwa_resolution_experiment.py`
- 详细文档：本说明文档

---

**实验版本**: v1.0  
**创建时间**: 2024年  
**基于论文**: 4.3节 选取合理离散动作分辨率

