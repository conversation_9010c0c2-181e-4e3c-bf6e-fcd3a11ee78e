"""
增强的预训练神经网络框架
Enhanced Pretrained Neural Network Framework

核心创新：
1. 预训练阶段：神经网络学习生成比DWA更好的候选动作
2. 在线阶段：直接生成高质量动作，无需暴力搜索
3. 硬约束验证：保证100%安全性
"""

import torch
import numpy as np
from typing import List, Dict, Tuple
from pretrained_action_generator import PretrainedActionGenerator
from hybrid_safe_constraint_predictor import HybridSafeConstraintPredictor

class EnhancedPretrainedFramework:
    """
    增强的预训练框架
    
    核心创新：
    - 预训练的神经网络替代DWA的暴力搜索
    - 保持硬约束验证的安全保证
    - 与强化学习无缝集成
    """
    
    def __init__(self, device='cpu'):
        self.device = device
        
        # 预训练动作生成器 - 核心创新
        self.action_generator = PretrainedActionGenerator(device)
        
        # 硬约束验证器 - 安全保证
        self.constraint_verifier = HybridSafeConstraintPredictor(device)
        
        # 训练状态
        self.is_ready = False
        
    def pretrain_action_generator(self, num_expert_episodes=500, num_epochs=200):
        """
        预训练动作生成器
        这是关键步骤：让神经网络学会生成比DWA更好的动作
        """
        print("🚀 开始预训练动作生成器...")
        
        # 1. 生成专家数据
        expert_data = self.action_generator.generate_expert_data(num_expert_episodes)
        
        # 2. 预训练网络
        losses = self.action_generator.pretrain(expert_data, num_epochs)
        
        # 3. 保存模型
        self.action_generator.save_model("pretrained_action_generator.pth")
        
        self.is_ready = True
        print("✅ 预训练完成!")
        
        return losses
    
    def load_pretrained_model(self, model_path="pretrained_action_generator.pth"):
        """加载预训练模型"""
        success = self.action_generator.load_model(model_path)
        if success:
            self.is_ready = True
        return success
    
    def generate_safe_action_set(self, state: np.ndarray, obstacles: List[Dict], 
                               goal: np.ndarray, num_candidates: int = 50) -> List[Dict]:
        """
        生成安全动作集 - 核心功能
        
        创新点：
        1. 神经网络直接生成高质量候选（替代DWA暴力搜索）
        2. 硬约束验证确保100%安全
        3. 比传统DWA更快更好
        """
        
        if not self.is_ready:
            print("警告：模型未准备好，使用备用方法")
            return self._fallback_generation(state, obstacles, goal, num_candidates)
        
        # 第一步：神经网络智能生成候选动作
        candidate_actions = self.action_generator.generate_candidate_actions(
            state, goal, obstacles, num_candidates
        )
        
        # 第二步：硬约束验证
        verified_safe_actions = []
        
        for action in candidate_actions:
            # 预测轨迹
            trajectory = self.constraint_verifier._predict_trajectory(state, action)
            
            # 硬约束验证
            if self.constraint_verifier._verify_trajectory_safety(trajectory, obstacles, state):
                # 评价动作质量
                score = self.constraint_verifier._evaluate_action(
                    state, action, goal, obstacles, trajectory
                )
                
                verified_safe_actions.append({
                    'action': action,
                    'trajectory': trajectory,
                    'safety_score': 1.0,  # 通过硬约束验证，100%安全
                    'total_score': score,
                    'method': 'neural_generated'
                })
        
        # 如果神经网络生成的动作都不安全，使用保守策略
        if not verified_safe_actions:
            print("警告：神经网络生成的动作都不安全，使用保守策略")
            conservative_actions = self.constraint_verifier._generate_conservative_actions(
                state, goal, obstacles
            )
            verified_safe_actions.extend(conservative_actions)
        
        # 按评分排序
        verified_safe_actions.sort(key=lambda x: x['total_score'], reverse=True)
        
        return verified_safe_actions[:20]
    
    def _fallback_generation(self, state: np.ndarray, obstacles: List[Dict], 
                           goal: np.ndarray, num_candidates: int) -> List[Dict]:
        """备用动作生成方法"""
        return self.constraint_verifier.generate_safe_action_set(
            state, obstacles, goal, num_candidates
        )
    
    def compare_with_dwa(self, test_states: List[np.ndarray], 
                        test_obstacles: List[List[Dict]], 
                        test_goals: List[np.ndarray]) -> Dict:
        """
        与传统DWA性能对比
        """
        print("⚖️ 与传统DWA性能对比...")
        
        results = {
            'neural_times': [],
            'neural_action_counts': [],
            'neural_quality_scores': [],
            'dwa_times': [],
            'dwa_action_counts': [],
            'dwa_quality_scores': []
        }
        
        import time
        
        for i, (state, obstacles, goal) in enumerate(zip(test_states, test_obstacles, test_goals)):
            # 测试神经网络方法
            start_time = time.time()
            neural_actions = self.generate_safe_action_set(state, obstacles, goal, 50)
            neural_time = time.time() - start_time
            
            results['neural_times'].append(neural_time)
            results['neural_action_counts'].append(len(neural_actions))
            if neural_actions:
                results['neural_quality_scores'].append(neural_actions[0]['total_score'])
            else:
                results['neural_quality_scores'].append(0.0)
            
            # 测试传统DWA方法（简化版）
            start_time = time.time()
            dwa_actions = self.constraint_verifier.generate_safe_action_set(
                state, obstacles, goal, 50
            )
            dwa_time = time.time() - start_time
            
            results['dwa_times'].append(dwa_time)
            results['dwa_action_counts'].append(len(dwa_actions))
            if dwa_actions:
                results['dwa_quality_scores'].append(dwa_actions[0]['total_score'])
            else:
                results['dwa_quality_scores'].append(0.0)
            
            if i % 10 == 0:
                print(f"  测试进度: {i+1}/{len(test_states)}")
        
        # 计算统计结果
        neural_avg_time = np.mean(results['neural_times'])
        dwa_avg_time = np.mean(results['dwa_times'])
        neural_avg_quality = np.mean(results['neural_quality_scores'])
        dwa_avg_quality = np.mean(results['dwa_quality_scores'])
        
        speedup = dwa_avg_time / neural_avg_time if neural_avg_time > 0 else 0
        quality_improvement = (neural_avg_quality - dwa_avg_quality) / dwa_avg_quality * 100 if dwa_avg_quality > 0 else 0
        
        print(f"\n📊 性能对比结果:")
        print(f"  神经网络方法:")
        print(f"    平均时间: {neural_avg_time:.4f}s")
        print(f"    平均质量: {neural_avg_quality:.3f}")
        print(f"  传统DWA方法:")
        print(f"    平均时间: {dwa_avg_time:.4f}s")
        print(f"    平均质量: {dwa_avg_quality:.3f}")
        print(f"  性能提升:")
        print(f"    速度提升: {speedup:.1f}x")
        print(f"    质量提升: {quality_improvement:.1f}%")
        
        return {
            'speedup': speedup,
            'quality_improvement': quality_improvement,
            'neural_avg_time': neural_avg_time,
            'dwa_avg_time': dwa_avg_time,
            'neural_avg_quality': neural_avg_quality,
            'dwa_avg_quality': dwa_avg_quality
        }

# 集成到强化学习框架
class EnhancedNeuralRL:
    """
    增强的神经网络强化学习框架
    集成预训练动作生成器
    """
    
    def __init__(self, state_dim=6, action_dim=3, device='cpu'):
        self.device = device
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # 预训练框架 - 替代传统DWA
        self.pretrained_framework = EnhancedPretrainedFramework(device)
        
        # TD3组件（简化版）
        from neural_enhanced_rl_framework import Actor, Critic
        self.actor = Actor(state_dim, action_dim).to(device)
        self.critic1 = Critic(state_dim, action_dim).to(device)
        self.critic2 = Critic(state_dim, action_dim).to(device)
        
        # 训练参数
        self.current_episode = 0
        self.constraint_guidance_episodes = 100
        
    def get_action(self, state: np.ndarray, obstacles: List[Dict], 
                   goal: np.ndarray, training: bool = True) -> Tuple[np.ndarray, Dict]:
        """
        分层动作选择 - 使用预训练神经网络
        """
        
        # 第一层：预训练神经网络生成安全候选动作
        safe_candidates = self.pretrained_framework.generate_safe_action_set(
            state, obstacles, goal, num_candidates=50
        )
        
        if not safe_candidates:
            # 紧急情况
            emergency_action = np.array([0.0, 0.0, 0.0])
            return emergency_action, {
                'num_safe_candidates': 0,
                'selection_method': 'emergency',
                'top_candidate_score': 0.0
            }
        
        # 第二层：TD3网络选择最优动作
        if self.current_episode < self.constraint_guidance_episodes:
            # 早期阶段：主要依赖神经网络生成的候选
            selected_action = self._neural_guided_selection(safe_candidates)
            selection_method = 'neural_guided'
        else:
            # 后期阶段：TD3主导选择
            selected_action = self._policy_guided_selection(safe_candidates, state, training)
            selection_method = 'policy_guided'
        
        info = {
            'num_safe_candidates': len(safe_candidates),
            'selection_method': selection_method,
            'top_candidate_score': safe_candidates[0]['total_score']
        }
        
        return selected_action, info
    
    def _neural_guided_selection(self, candidates: List[Dict]) -> np.ndarray:
        """神经网络引导的动作选择"""
        if not candidates:
            return np.array([0.0, 0.0, 0.0])
        
        # 在前几个最优候选中随机选择
        top_k = min(3, len(candidates))
        selected_idx = np.random.randint(0, top_k)
        
        return candidates[selected_idx]['action']
    
    def _policy_guided_selection(self, candidates: List[Dict], 
                               state: np.ndarray, training: bool) -> np.ndarray:
        """策略引导的动作选择"""
        if not candidates:
            return np.array([0.0, 0.0, 0.0])
        
        # TD3生成期望动作
        state_tensor = torch.FloatTensor(state[:self.state_dim]).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            td3_action = self.actor(state_tensor).cpu().numpy()[0]
        
        # 从候选集中找到最接近TD3期望的安全动作
        candidate_actions = np.array([c['action'] for c in candidates])
        distances = np.linalg.norm(candidate_actions - td3_action, axis=1)
        
        # 结合距离和安全评分选择
        scores = np.array([c['total_score'] for c in candidates])
        normalized_distances = 1.0 / (1.0 + distances)
        
        # 综合评分
        combined_scores = 0.7 * scores + 0.3 * normalized_distances
        
        best_idx = np.argmax(combined_scores)
        return candidates[best_idx]['action']
    
    def update_episode_count(self):
        """更新episode计数"""
        self.current_episode += 1
