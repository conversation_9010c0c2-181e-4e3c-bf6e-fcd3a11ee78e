"""
主实验运行脚本 - 基于SCI期刊草稿的完整仿真实验
整合所有实验模块，提供统一的实验入口

实验内容：
1. 分阶段课程学习验证
2. 约束满足性能分析
3. 算法对比测试
4. 消融实验
5. 三维空间性能验证
6. 轨迹规划质量分析
"""

import sys
import os
import argparse
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入实验模块
from comprehensive_simulation_experiment import ComprehensiveSimulationExperiment
from algorithm_comparison_framework import AlgorithmComparisonFramework
from constraint_analysis_framework import ConstraintAnalysisFramework
from staged_training_framework import LoiteringMunitionStagedTrainer

def print_experiment_menu():
    """打印实验菜单"""
    print("\n🔬 基于约束感知强化学习的巡飞弹分层运动规划方法 - 仿真实验系统")
    print("=" * 80)
    print("📋 可用实验:")
    print("1. 综合仿真实验 (完整验证所有创新点)")
    print("2. 分阶段课程学习验证")
    print("3. 约束满足性能分析")
    print("4. 算法对比测试")
    print("5. 消融实验")
    print("6. 三维空间性能验证")
    print("7. 轨迹规划质量分析")
    print("8. 快速验证实验")
    print("9. 自定义实验")
    print("0. 退出")
    print("=" * 80)

def run_comprehensive_experiment():
    """运行综合仿真实验"""
    print("\n🚀 开始综合仿真实验...")
    print("=" * 60)
    
    experiment = ComprehensiveSimulationExperiment()
    results = experiment.run_comprehensive_experiment()
    
    if results:
        print("\n✅ 综合仿真实验完成!")
        print(f"📁 结果保存在: {experiment.experiment_dir}")
    else:
        print("\n❌ 综合仿真实验失败!")
    
    return results

def run_curriculum_learning_experiment():
    """运行分阶段课程学习验证"""
    print("\n📚 开始分阶段课程学习验证...")
    print("=" * 60)
    
    experiment = ComprehensiveSimulationExperiment("curriculum_learning_results")
    results = experiment._run_curriculum_learning_experiment()
    
    if results:
        print("\n✅ 分阶段课程学习验证完成!")
        print(f"📁 结果保存在: {experiment.experiment_dir}")
    else:
        print("\n❌ 分阶段课程学习验证失败!")
    
    return results

def run_constraint_analysis_experiment():
    """运行约束满足性能分析"""
    print("\n🛡️ 开始约束满足性能分析...")
    print("=" * 60)
    
    from constraint_analysis_framework import ConstraintAnalysisFramework
    framework = ConstraintAnalysisFramework()
    results = framework.run_comprehensive_constraint_analysis()
    
    if results:
        print("\n✅ 约束满足性能分析完成!")
        print(f"📁 结果保存在: {framework.experiment_dir}")
    else:
        print("\n❌ 约束满足性能分析失败!")
    
    return results

def run_algorithm_comparison_experiment():
    """运行算法对比测试"""
    print("\n⚖️ 开始算法对比测试...")
    print("=" * 60)
    
    from algorithm_comparison_framework import AlgorithmComparisonFramework
    framework = AlgorithmComparisonFramework()
    results = framework.run_comprehensive_comparison()
    
    if results:
        print("\n✅ 算法对比测试完成!")
        print(f"📁 结果保存在: {framework.experiment_dir}")
    else:
        print("\n❌ 算法对比测试失败!")
    
    return results

def run_ablation_study_experiment():
    """运行消融实验"""
    print("\n🔍 开始消融实验...")
    print("=" * 60)
    
    experiment = ComprehensiveSimulationExperiment("ablation_study_results")
    results = experiment._run_ablation_study()
    
    if results:
        print("\n✅ 消融实验完成!")
        print(f"📁 结果保存在: {experiment.experiment_dir}")
    else:
        print("\n❌ 消融实验失败!")
    
    return results

def run_3d_performance_experiment():
    """运行三维空间性能验证"""
    print("\n🌐 开始三维空间性能验证...")
    print("=" * 60)
    
    experiment = ComprehensiveSimulationExperiment("3d_performance_results")
    results = experiment._run_3d_performance_validation()
    
    if results:
        print("\n✅ 三维空间性能验证完成!")
        print(f"📁 结果保存在: {experiment.experiment_dir}")
    else:
        print("\n❌ 三维空间性能验证失败!")
    
    return results

def run_trajectory_quality_experiment():
    """运行轨迹规划质量分析"""
    print("\n🎯 开始轨迹规划质量分析...")
    print("=" * 60)
    
    experiment = ComprehensiveSimulationExperiment("trajectory_quality_results")
    results = experiment._run_trajectory_quality_analysis()
    
    if results:
        print("\n✅ 轨迹规划质量分析完成!")
        print(f"📁 结果保存在: {experiment.experiment_dir}")
    else:
        print("\n❌ 轨迹规划质量分析失败!")
    
    return results

def run_quick_validation_experiment():
    """运行快速验证实验"""
    print("\n🧪 开始快速验证实验...")
    print("=" * 60)
    
    from comprehensive_simulation_experiment import run_quick_validation
    results = run_quick_validation()
    
    if results:
        print("\n✅ 快速验证实验完成!")
    else:
        print("\n❌ 快速验证实验失败!")
    
    return results

def run_custom_experiment():
    """运行自定义实验"""
    print("\n🎨 自定义实验配置...")
    print("=" * 60)
    
    print("请选择实验类型:")
    print("1. 单阶段训练")
    print("2. 特定环境测试")
    print("3. 参数调优实验")
    print("4. 性能基准测试")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            stage = input("请输入阶段号 (1-3): ").strip()
            if stage in ["1", "2", "3"]:
                stage_num = int(stage)
                print(f"\n🎯 开始阶段 {stage_num} 训练...")
                
                trainer = LoiteringMunitionStagedTrainer(
                    start_stage=stage_num,
                    end_stage=stage_num,
                    seed=42,
                    visualization_interval=10
                )
                
                results, controller = trainer.run_staged_training()
                
                if results:
                    print(f"\n✅ 阶段 {stage_num} 训练完成!")
                    print(f"📁 结果保存在: {trainer.output_dir}")
                else:
                    print(f"\n❌ 阶段 {stage_num} 训练失败!")
                
                return results
            else:
                print("❌ 无效的阶段号")
                return None
        
        elif choice == "2":
            print("特定环境测试功能待实现...")
            return None
        
        elif choice == "3":
            print("参数调优实验功能待实现...")
            return None
        
        elif choice == "4":
            print("性能基准测试功能待实现...")
            return None
        
        else:
            print("❌ 无效的选择")
            return None
            
    except KeyboardInterrupt:
        print("\n👋 用户取消")
        return None

def run_interactive_mode():
    """交互式运行模式"""
    while True:
        print_experiment_menu()
        
        try:
            choice = input("\n请选择实验 (0-9): ").strip()
            
            if choice == "0":
                print("👋 再见!")
                break
            
            elif choice == "1":
                run_comprehensive_experiment()
            
            elif choice == "2":
                run_curriculum_learning_experiment()
            
            elif choice == "3":
                run_constraint_analysis_experiment()
            
            elif choice == "4":
                run_algorithm_comparison_experiment()
            
            elif choice == "5":
                run_ablation_study_experiment()
            
            elif choice == "6":
                run_3d_performance_experiment()
            
            elif choice == "7":
                run_trajectory_quality_experiment()
            
            elif choice == "8":
                run_quick_validation_experiment()
            
            elif choice == "9":
                run_custom_experiment()
            
            else:
                print("❌ 无效的选择，请重新输入")
            
            # 询问是否继续
            if choice != "0":
                continue_choice = input("\n是否继续其他实验? (y/N): ").strip().lower()
                if continue_choice != 'y':
                    print("👋 再见!")
                    break
                
        except KeyboardInterrupt:
            print("\n👋 用户取消")
            break

def run_batch_mode(experiments):
    """批量运行模式"""
    print(f"\n🚀 批量运行 {len(experiments)} 个实验...")
    print("=" * 60)
    
    results = {}
    
    for i, experiment in enumerate(experiments, 1):
        print(f"\n📋 实验 {i}/{len(experiments)}: {experiment}")
        print("-" * 40)
        
        try:
            if experiment == "comprehensive":
                result = run_comprehensive_experiment()
            elif experiment == "curriculum":
                result = run_curriculum_learning_experiment()
            elif experiment == "constraint":
                result = run_constraint_analysis_experiment()
            elif experiment == "comparison":
                result = run_algorithm_comparison_experiment()
            elif experiment == "ablation":
                result = run_ablation_study_experiment()
            elif experiment == "3d_performance":
                result = run_3d_performance_experiment()
            elif experiment == "trajectory":
                result = run_trajectory_quality_experiment()
            elif experiment == "quick":
                result = run_quick_validation_experiment()
            else:
                print(f"❌ 未知的实验类型: {experiment}")
                result = None
            
            results[experiment] = result
            
        except Exception as e:
            print(f"❌ 实验 {experiment} 失败: {e}")
            results[experiment] = None
    
    print(f"\n✅ 批量实验完成! 成功: {sum(1 for r in results.values() if r is not None)}/{len(experiments)}")
    return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='基于约束感知强化学习的巡飞弹分层运动规划方法 - 仿真实验系统')
    parser.add_argument('--mode', choices=['interactive', 'batch', 'single'], default='interactive',
                       help='运行模式: interactive(交互式), batch(批量), single(单个)')
    parser.add_argument('--experiment', choices=['comprehensive', 'curriculum', 'constraint', 'comparison', 
                                                'ablation', '3d_performance', 'trajectory', 'quick'],
                       help='单个实验类型')
    parser.add_argument('--experiments', nargs='+', 
                       choices=['comprehensive', 'curriculum', 'constraint', 'comparison', 
                               'ablation', '3d_performance', 'trajectory', 'quick'],
                       help='批量实验类型列表')
    
    args = parser.parse_args()
    
    print("🔬 基于约束感知强化学习的巡飞弹分层运动规划方法")
    print("📅 仿真实验系统")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    if args.mode == 'interactive':
        run_interactive_mode()
    
    elif args.mode == 'batch':
        if not args.experiments:
            print("❌ 批量模式需要指定 --experiments 参数")
            return
        run_batch_mode(args.experiments)
    
    elif args.mode == 'single':
        if not args.experiment:
            print("❌ 单个模式需要指定 --experiment 参数")
            return
        
        print(f"\n🎯 运行单个实验: {args.experiment}")
        print("=" * 60)
        
        if args.experiment == "comprehensive":
            run_comprehensive_experiment()
        elif args.experiment == "curriculum":
            run_curriculum_learning_experiment()
        elif args.experiment == "constraint":
            run_constraint_analysis_experiment()
        elif args.experiment == "comparison":
            run_algorithm_comparison_experiment()
        elif args.experiment == "ablation":
            run_ablation_study_experiment()
        elif args.experiment == "3d_performance":
            run_3d_performance_experiment()
        elif args.experiment == "trajectory":
            run_trajectory_quality_experiment()
        elif args.experiment == "quick":
            run_quick_validation_experiment()

def print_experiment_summary():
    """打印实验摘要"""
    print("\n📊 实验摘要")
    print("=" * 60)
    print("本仿真实验系统验证了基于约束感知强化学习的巡飞弹分层运动规划方法")
    print("的主要创新点：")
    print()
    print("🎯 主要创新点验证：")
    print("1. 三维空间约束感知DWA扩展")
    print("   - 从2D扩展到3D，解决维度灾难问题")
    print("   - 建立基于约束感知的动作空间")
    print("   - 通过控制输入空间采样替代状态空间采样")
    print()
    print("2. 分层安全强化学习架构")
    print("   - DWA层负责实时安全约束")
    print("   - TD3层负责全局策略优化")
    print("   - 前置约束验证实现主动安全保障")
    print()
    print("3. 分阶段课程学习")
    print("   - 从简单到复杂的渐进式训练")
    print("   - 解决安全强化学习中的探索-安全矛盾")
    print("   - 最大化任务效率的同时保证零约束违反")
    print()
    print("4. 零约束违反率保证")
    print("   - 100%约束满足率")
    print("   - 运动学约束、安全约束、边界约束")
    print("   - 长时间运行稳定性验证")
    print()
    print("🔬 实验验证内容：")
    print("- 分阶段课程学习效果验证")
    print("- 约束满足性能详细分析")
    print("- 与基线算法的全面对比")
    print("- 消融实验验证各组件贡献")
    print("- 三维空间性能验证")
    print("- 轨迹规划质量分析")
    print()
    print("📈 预期实验结果：")
    print("- 零约束违反率")
    print("- 优秀的路径效率")
    print("- 良好的机动性能")
    print("- 稳定的长时间运行")
    print("- 优于基线算法的综合性能")

if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) == 1:
        # 没有命令行参数，显示实验摘要并运行交互模式
        print_experiment_summary()
        run_interactive_mode()
    else:
        # 有命令行参数，运行指定模式
        main()
